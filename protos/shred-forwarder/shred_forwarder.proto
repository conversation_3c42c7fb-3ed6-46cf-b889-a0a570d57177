syntax = "proto3";

package shred_forwarder;

import "google/protobuf/timestamp.proto";

service ShredForwarderService {
    rpc Ping(PingRequest) returns (PongResponse);
    rpc Subscribe(SubscribeRequest) returns (stream SubscribeResponse);
}

message PingRequest {
    int32 count = 1;
}

message SubscribeRequest {
}

message PongResponse {
    int32 count = 1;
    google.protobuf.Timestamp timestamp = 2;
    string version = 3;
}

message SubscribeResponse {
    uint64 slot = 1;
    uint32 entry_index = 2;
    uint32 transaction_index = 3;
    VersionedTransaction transaction = 4;
    google.protobuf.Timestamp received_at = 5;
    uint64 processing_time_nanos = 6;
}

message VersionedTransaction {
    repeated bytes signatures = 1;
    VersionedMessage message = 2;
}

message VersionedMessage {
    oneof message_type {
        LegacyMessage legacy = 1;
        V0Message v0 = 2;
    }
}

message LegacyMessage {
    MessageHeader header = 1;
    repeated bytes account_keys = 2;
    bytes recent_blockhash = 3;
    repeated CompiledInstruction instructions = 4;
}

message V0Message {
    MessageHeader header = 1;
    repeated bytes account_keys = 2;
    bytes recent_blockhash = 3;
    repeated CompiledInstruction instructions = 4;
    repeated MessageAddressTableLookup address_table_lookups = 5;
}

message MessageHeader {
    uint32 num_required_signatures = 1;
    uint32 num_readonly_signed_accounts = 2;
    uint32 num_readonly_unsigned_accounts = 3;
}

message CompiledInstruction {
    uint32 program_id_index = 1;
    repeated uint32 accounts = 2;
    bytes data = 3;
}

message MessageAddressTableLookup {
    bytes account_key = 1;
    repeated uint32 writable_indexes = 2;
    repeated uint32 readonly_indexes = 3;
}
