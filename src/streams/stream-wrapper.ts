import type { ClientReadableStream, StatusObject } from '@grpc/grpc-js'
import { notNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import type { Fn } from '@kdt310722/utils/function'
import { resolveNestedOptions } from '@kdt310722/utils/object'
import { type Awaitable, type DeferredPromise, createDeferred } from '@kdt310722/utils/promise'
import { IdleTimeoutError, StreamError } from '../errors'
import { isCancelledGrpcStatus, isGrpcError } from '../utils'
import { type CreateStreamEventHandlers, subscribeStream } from './create-stream'
import { IdleTimer } from './idle-timer'
import { ResubscribeReason, Resubscriber, type ResubscriberEvents, type ResubscriberOptions } from './resubscriber'

export enum StreamState {
    CLOSING = 'CLOSING',
    CLOSED = 'CLOSED',
    SUBSCRIBING = 'SUBSCRIBING',
    SUBSCRIBED = 'SUBSCRIBED',
}

export interface StreamWrapperTimeoutOptions {
    subscribe?: number
    close?: number
    idle?: number | boolean
}

export interface StreamWrapperOptions {
    timeout?: StreamWrapperTimeoutOptions
    resubscribe?: ResubscriberOptions | boolean
}

export type StreamWrapperEvents<TData> = ResubscriberEvents & {
    state: (state: StreamState) => void
    error: (error: unknown) => void
    data: (data: TData) => void
    closed: (isExplicitly: boolean, error?: unknown) => void
}

export class StreamWrapper<TResponse, TStream extends ClientReadableStream<TResponse> = ClientReadableStream<TResponse>> extends Emitter<StreamWrapperEvents<TResponse>> {
    protected readonly closeTimeout: number
    protected readonly subscribeTimeout: number
    protected readonly subscribeEventHandlers: CreateStreamEventHandlers<TStream> = {}

    protected readonly streamListeners: Record<string, Fn> = {}
    protected readonly idleTimer?: IdleTimer
    protected readonly resubscriber: Resubscriber<TResponse, TStream>

    protected stream?: TStream
    protected lastError?: unknown
    protected isExplicitlyClosed = false
    protected closePromise?: DeferredPromise<void>
    protected abortController = new AbortController()

    #state: StreamState = StreamState.CLOSED

    public constructor(protected readonly subscriber: (signal: AbortSignal) => Awaitable<TStream>, { timeout = {}, resubscribe = true }: StreamWrapperOptions = {}) {
        super()

        this.subscribeTimeout = timeout.subscribe ?? 10_000
        this.closeTimeout = timeout.close ?? this.subscribeTimeout
        this.idleTimer = this.createIdleTimer(timeout.idle)
        this.resubscriber = new Resubscriber(this, resolveNestedOptions(resubscribe) || { enabled: false })
    }

    public get state() {
        return this.#state
    }

    public async subscribe(signal?: AbortSignal) {
        console.log('call subscribe')

        const state = this.state

        if (state === StreamState.SUBSCRIBED) {
            return
        }

        if (state !== StreamState.CLOSED) {
            throw new StreamError(`Stream is already in state: ${state}`)
        }

        this.setState(StreamState.SUBSCRIBING)
        this.stream = await subscribeStream(this.subscriber.bind(this), { signal, timeout: this.subscribeTimeout })

        const handlers = {
            data: this.handleData.bind(this, this.stream),
            error: this.handleError.bind(this, this.stream),
            status: this.handleStatus.bind(this, this.stream),
            close: this.handleClose.bind(this, this.stream),
            pause: () => this.idleTimer?.pause(),
            resume: () => this.idleTimer?.resume(),
        }

        for (const [event, handler] of Object.entries(handlers)) {
            conónốnel
            this.stream.on(event, this.streamListeners[event] = handler)
        }

        console.log('event registered')

        this.idleTimer?.start()
        this.setState(StreamState.SUBSCRIBED)
    }

    public async close(isExplicitly = true, destroyOnTimeout = true, destroyOnCancelFailure = true) {
        if (!this.abortController.signal.aborted) {
            this.abortController.abort()
        }

        const state = this.state

        if (state === StreamState.CLOSED) {
            return
        }

        if (state !== StreamState.SUBSCRIBED) {
            throw new StreamError(`Stream is not in a state to close: ${state}`)
        }

        this.setState(StreamState.CLOSING)

        this.isExplicitlyClosed = isExplicitly
        this.closePromise = createDeferred<void>()

        const handleTimeout = () => {
            const error = new StreamError('Stream close timeout')

            if (destroyOnTimeout) {
                this.stream?.destroy(error)
            } else {
                this.closePromise?.reject(error)
            }
        }

        const timer = setTimeout(handleTimeout, this.closeTimeout)

        try {
            this.stream?.cancel()
        } catch (error) {
            if (destroyOnCancelFailure) {
                this.stream?.destroy(error instanceof Error ? error : new StreamError('Stream cancel failed', { cause: error }))
            } else {
                this.closePromise.reject(error)
            }
        }

        return this.closePromise.finally(() => {
            clearTimeout(timer)
            this.closePromise = undefined
        })
    }

    protected handleData(_stream: TStream, data: TResponse) {
        this.emitData(data)
        this.idleTimer?.reset()
    }

    protected emitData(data: TResponse) {
        this.emit('data', data)
    }

    protected handleStatus(stream: TStream, status: StatusObject) {
        this.handleClose(stream, status, 'status')
    }

    protected handleClose(_stream: TStream, status?: StatusObject, from = 'close') {
        console.log('handleClose from', from)

        const isExplicitly = this.isExplicitlyClosed
        const error = this.lastError ?? status

        this.reset()

        if (this.closePromise) {
            this.closePromise.resolve()
        }

        this.emit('closed', isExplicitly, error)

        if (isExplicitly) {
            this.resubscriber.reset()
            this.emit('resubscribeAbandoned', ResubscribeReason.EXPLICITLY_CLOSED)
        } else {
            this.resubscriber.resubscribe(error, this.abortController.signal)
        }
    }

    protected handleError(_stream: TStream, error: unknown) {
        console.log('handleError')

        if (isGrpcError(error) && isCancelledGrpcStatus(error)) {
            return
        }

        this.emit('error', this.lastError = error instanceof Error ? error : new StreamError('An error occurred', { cause: error }))
    }

    protected reset() {
        this.idleTimer?.stop()
        this.isExplicitlyClosed = false
        this.lastError = undefined
        this.abortController.abort()
        this.abortController = new AbortController()

        if (notNullish(this.stream)) {
            for (const [event, handler] of Object.entries(this.streamListeners)) {
                this.stream.removeListener(event, handler)
            }

            this.stream = undefined
        }

        this.setState(StreamState.CLOSED)
    }

    protected setState(state: StreamState) {
        this.emit('state', this.#state = state)
    }

    protected createIdleTimer(timeout: number | boolean = true) {
        return timeout === false ? undefined : new IdleTimer(timeout === true ? 60_000 : timeout, () => this.stream?.destroy(new IdleTimeoutError()))
    }

    protected addSubscribeEventHandler(event: string, handler: CreateStreamEventHandlers<TStream>[string]) {
        this.subscribeEventHandlers[event] = handler
    }
}
