import type { CallOptions, Client, ClientDuplexStream, Metadata, StatusObject } from '@grpc/grpc-js'
import { notNullish } from '@kdt310722/utils/common'
import { type AnyObject, isObject } from '@kdt310722/utils/object'
import { DuplexStreamWrapper, type DuplexStreamWrapperOptions } from '../../../streams/duplex-stream-wrapper'
import { EMPTY_METADATA } from '../../../utils'
import type { SubscribeRequest, SubscribeUpdate } from '../generated/geyser'
import { resolveHeartbeatOptions } from './options'

export type YellowstoneGeyserSubscribeStream = ClientDuplexStream<SubscribeRequest, SubscribeUpdate>

export interface YellowstoneGeyserStreamWrapperOptions<TRequest = SubscribeRequest> extends DuplexStreamWrapperOptions<TRequest> {
    metadata?: Metadata
    callOptions?: CallOptions
    sendLastRequestOnResubscribed?: boolean
    emitPingResponse?: boolean
    updateOnPing?: boolean
}

export class YellowstoneGeyserStreamWrapper<TRequest extends AnyObject = SubscribeRequest, TResponse extends AnyObject = SubscribeUpdate> extends DuplexStreamWrapper<TRequest, TResponse> {
    protected readonly sendLastRequestOnResubscribed: boolean
    protected readonly emitPingResponse: boolean
    protected readonly updateOnPing: boolean

    protected lastRequest?: TRequest

    public constructor(protected readonly client: Client & { subscribe: (metadata: Metadata, callOptions?: CallOptions) => ClientDuplexStream<TRequest, TResponse> }, { metadata = EMPTY_METADATA, callOptions, heartbeat = true, sendLastRequestOnResubscribed = true, emitPingResponse = false, updateOnPing = true, ...options }: YellowstoneGeyserStreamWrapperOptions<TRequest> = {}) {
        super(() => this.client.subscribe(metadata, callOptions), { heartbeat: resolveHeartbeatOptions(heartbeat), ...options })

        this.sendLastRequestOnResubscribed = sendLastRequestOnResubscribed
        this.emitPingResponse = emitPingResponse
        this.updateOnPing = updateOnPing

        this.on('wrote', this.handleWrote.bind(this))
        this.resubscriber.onResubscribed(this.handleResubscribed.bind(this))
    }

    public update() {
        if (notNullish(this.lastRequest)) {
            return this.write(this.lastRequest)
        }
    }

    protected override handleClose(stream: ClientDuplexStream<TRequest, TResponse>, status?: StatusObject) {
        if (this.isExplicitlyClosed) {
            this.lastRequest = undefined
        }

        return super.handleClose(stream, status)
    }

    protected override emitData(data: TResponse) {
        if (this.isPingResponseMessage(data)) {
            if (this.updateOnPing) {
                this.update()
            }

            if (!this.emitPingResponse) {
                return
            }
        }

        super.emitData(data)
    }

    protected handleWrote(data: TRequest) {
        if (!this.isPingRequestMessage(data)) {
            this.lastRequest = data
        }
    }

    protected handleResubscribed() {
        if (this.sendLastRequestOnResubscribed) {
            this.update()
        }
    }

    protected isPingRequestMessage(request: TRequest) {
        return notNullish(request.ping?.id)
    }

    protected isPingResponseMessage(response: TResponse) {
        return 'ping' in response && isObject(response.ping)
    }
}
