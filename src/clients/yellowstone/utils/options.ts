import { resolveNestedOptions } from '@kdt310722/utils/object'
import type { HeartbeatManagerOptions } from '../../../streams/heartbeat-manager'
import type { SubscribeRequest } from '../generated/geyser'
import { createPingMessage } from './requests'

export function resolveHeartbeatOptions<TRequest = SubscribeRequest>(options: HeartbeatManagerOptions<TRequest> | boolean) {
    const resolved = resolveNestedOptions(options) || { enabled: false }

    resolved.message ??= createPingMessage() as TRequest

    return resolved
}
