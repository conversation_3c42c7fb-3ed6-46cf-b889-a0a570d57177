import { type CreateGrpcClientOptions, createGrpcClient } from '../../utils'
import { ShredstreamProxyClient } from './generated/shredstream'
import { JitoShredStreamStreamWrapper, type JitoShredStreamStreamWrapperOptions } from './utils'

export type JitoShredStreamClientOptions = CreateGrpcClientOptions

export class JitoShredStreamClient {
    public readonly grpc: ShredstreamProxyClient

    public constructor(url: string, protected readonly options: JitoShredStreamClientOptions = {}) {
        this.grpc = createGrpcClient(ShredstreamProxyClient, url, options)
    }

    public createStream(options?: JitoShredStreamStreamWrapperOptions) {
        return new JitoShredStreamStreamWrapper(this.grpc, options)
    }
}
