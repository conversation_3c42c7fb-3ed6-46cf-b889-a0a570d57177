// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.1
//   protoc               v5.29.3
// source: shared.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Timestamp } from "./google/protobuf/timestamp";

export interface Header {
  ts: Date | undefined;
}

export interface Heartbeat {
  count: bigint;
}

export interface Socket {
  ip: string;
  port: bigint;
}

function createBaseHeader(): Header {
  return { ts: undefined };
}

export const Header: MessageFns<Header> = {
  encode(message: Header, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ts !== undefined) {
      Timestamp.encode(toTimestamp(message.ts), writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Header {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHeader();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.ts = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Header, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Header | Header[]> | Iterable<Header | Header[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Header.encode(p).finish()];
        }
      } else {
        yield* [Header.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Header>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Header> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Header.decode(p)];
        }
      } else {
        yield* [Header.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Header {
    return { ts: isSet(object.ts) ? fromJsonTimestamp(object.ts) : undefined };
  },

  toJSON(message: Header): unknown {
    const obj: any = {};
    if (message.ts !== undefined) {
      obj.ts = message.ts.toISOString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Header>, I>>(base?: I): Header {
    return Header.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Header>, I>>(object: I): Header {
    const message = createBaseHeader();
    message.ts = object.ts ?? undefined;
    return message;
  },
};

function createBaseHeartbeat(): Heartbeat {
  return { count: 0n };
}

export const Heartbeat: MessageFns<Heartbeat> = {
  encode(message: Heartbeat, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.count !== 0n) {
      if (BigInt.asUintN(64, message.count) !== message.count) {
        throw new globalThis.Error("value provided for field message.count of type uint64 too large");
      }
      writer.uint32(8).uint64(message.count);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Heartbeat {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHeartbeat();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.count = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Heartbeat, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Heartbeat | Heartbeat[]> | Iterable<Heartbeat | Heartbeat[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Heartbeat.encode(p).finish()];
        }
      } else {
        yield* [Heartbeat.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Heartbeat>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Heartbeat> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Heartbeat.decode(p)];
        }
      } else {
        yield* [Heartbeat.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Heartbeat {
    return { count: isSet(object.count) ? BigInt(object.count) : 0n };
  },

  toJSON(message: Heartbeat): unknown {
    const obj: any = {};
    if (message.count !== 0n) {
      obj.count = message.count.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Heartbeat>, I>>(base?: I): Heartbeat {
    return Heartbeat.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Heartbeat>, I>>(object: I): Heartbeat {
    const message = createBaseHeartbeat();
    message.count = object.count ?? 0n;
    return message;
  },
};

function createBaseSocket(): Socket {
  return { ip: "", port: 0n };
}

export const Socket: MessageFns<Socket> = {
  encode(message: Socket, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ip !== "") {
      writer.uint32(10).string(message.ip);
    }
    if (message.port !== 0n) {
      if (BigInt.asIntN(64, message.port) !== message.port) {
        throw new globalThis.Error("value provided for field message.port of type int64 too large");
      }
      writer.uint32(16).int64(message.port);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Socket {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSocket();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.ip = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.port = reader.int64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Socket, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Socket | Socket[]> | Iterable<Socket | Socket[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Socket.encode(p).finish()];
        }
      } else {
        yield* [Socket.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Socket>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Socket> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Socket.decode(p)];
        }
      } else {
        yield* [Socket.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Socket {
    return {
      ip: isSet(object.ip) ? globalThis.String(object.ip) : "",
      port: isSet(object.port) ? BigInt(object.port) : 0n,
    };
  },

  toJSON(message: Socket): unknown {
    const obj: any = {};
    if (message.ip !== "") {
      obj.ip = message.ip;
    }
    if (message.port !== 0n) {
      obj.port = message.port.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Socket>, I>>(base?: I): Socket {
    return Socket.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Socket>, I>>(object: I): Socket {
    const message = createBaseSocket();
    message.ip = object.ip ?? "";
    message.port = object.port ?? 0n;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | bigint | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function toTimestamp(date: Date): Timestamp {
  const seconds = BigInt(Math.trunc(date.getTime() / 1_000));
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (globalThis.Number(t.seconds.toString()) || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

function fromJsonTimestamp(o: any): Date {
  if (o instanceof globalThis.Date) {
    return o;
  } else if (typeof o === "string") {
    return new globalThis.Date(o);
  } else {
    return fromTimestamp(Timestamp.fromJSON(o));
  }
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  encodeTransform(source: AsyncIterable<T | T[]> | Iterable<T | T[]>): AsyncIterable<Uint8Array>;
  decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<T>;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
