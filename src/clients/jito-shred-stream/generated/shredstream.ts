// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.1
//   protoc               v5.29.3
// source: shredstream.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  type CallOptions,
  ChannelCredentials,
  Client,
  type ClientOptions,
  type ClientReadableStream,
  type ClientUnaryCall,
  type handleServerStreamingCall,
  type handleUnaryCall,
  makeGenericClientConstructor,
  Metadata,
  type ServiceError,
  type UntypedServiceImplementation,
} from "@grpc/grpc-js";
import { Timestamp } from "./google/protobuf/timestamp";
import { Socket } from "./shared";

export interface Heartbeat {
  /**
   * don't trust IP:PORT from tcp header since it can be tampered over the wire
   * `socket.ip` must match incoming packet's ip. this prevents spamming an unwitting destination
   */
  socket:
    | Socket
    | undefined;
  /**
   * regions for shredstream proxy to receive shreds from
   * list of valid regions: https://docs.jito.wtf/lowlatencytxnsend/#api
   */
  regions: string[];
}

export interface HeartbeatResponse {
  /** client must respond within `ttl_ms` to keep stream alive */
  ttlMs: number;
}

export interface TraceShred {
  /** source region, one of: https://docs.jito.wtf/lowlatencytxnsend/#api */
  region: string;
  /** timestamp of creation */
  createdAt:
    | Date
    | undefined;
  /** monotonically increases, resets upon service restart */
  seqNum: number;
}

/** tbd: we may want to add filters here */
export interface SubscribeEntriesRequest {
}

export interface Entry {
  /** the slot that the entry is from */
  slot: bigint;
  /** Serialized bytes of Vec<Entry>: https://docs.rs/solana-entry/latest/solana_entry/entry/struct.Entry.html */
  entries: Buffer;
}

function createBaseHeartbeat(): Heartbeat {
  return { socket: undefined, regions: [] };
}

export const Heartbeat: MessageFns<Heartbeat> = {
  encode(message: Heartbeat, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.socket !== undefined) {
      Socket.encode(message.socket, writer.uint32(10).fork()).join();
    }
    for (const v of message.regions) {
      writer.uint32(18).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Heartbeat {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHeartbeat();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.socket = Socket.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.regions.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Heartbeat, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Heartbeat | Heartbeat[]> | Iterable<Heartbeat | Heartbeat[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Heartbeat.encode(p).finish()];
        }
      } else {
        yield* [Heartbeat.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Heartbeat>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Heartbeat> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Heartbeat.decode(p)];
        }
      } else {
        yield* [Heartbeat.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Heartbeat {
    return {
      socket: isSet(object.socket) ? Socket.fromJSON(object.socket) : undefined,
      regions: globalThis.Array.isArray(object?.regions) ? object.regions.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: Heartbeat): unknown {
    const obj: any = {};
    if (message.socket !== undefined) {
      obj.socket = Socket.toJSON(message.socket);
    }
    if (message.regions?.length) {
      obj.regions = message.regions;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Heartbeat>, I>>(base?: I): Heartbeat {
    return Heartbeat.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Heartbeat>, I>>(object: I): Heartbeat {
    const message = createBaseHeartbeat();
    message.socket = (object.socket !== undefined && object.socket !== null)
      ? Socket.fromPartial(object.socket)
      : undefined;
    message.regions = object.regions?.map((e) => e) || [];
    return message;
  },
};

function createBaseHeartbeatResponse(): HeartbeatResponse {
  return { ttlMs: 0 };
}

export const HeartbeatResponse: MessageFns<HeartbeatResponse> = {
  encode(message: HeartbeatResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ttlMs !== 0) {
      writer.uint32(8).uint32(message.ttlMs);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HeartbeatResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHeartbeatResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.ttlMs = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<HeartbeatResponse, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<HeartbeatResponse | HeartbeatResponse[]> | Iterable<HeartbeatResponse | HeartbeatResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [HeartbeatResponse.encode(p).finish()];
        }
      } else {
        yield* [HeartbeatResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, HeartbeatResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<HeartbeatResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [HeartbeatResponse.decode(p)];
        }
      } else {
        yield* [HeartbeatResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): HeartbeatResponse {
    return { ttlMs: isSet(object.ttlMs) ? globalThis.Number(object.ttlMs) : 0 };
  },

  toJSON(message: HeartbeatResponse): unknown {
    const obj: any = {};
    if (message.ttlMs !== 0) {
      obj.ttlMs = Math.round(message.ttlMs);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<HeartbeatResponse>, I>>(base?: I): HeartbeatResponse {
    return HeartbeatResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HeartbeatResponse>, I>>(object: I): HeartbeatResponse {
    const message = createBaseHeartbeatResponse();
    message.ttlMs = object.ttlMs ?? 0;
    return message;
  },
};

function createBaseTraceShred(): TraceShred {
  return { region: "", createdAt: undefined, seqNum: 0 };
}

export const TraceShred: MessageFns<TraceShred> = {
  encode(message: TraceShred, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.region !== "") {
      writer.uint32(10).string(message.region);
    }
    if (message.createdAt !== undefined) {
      Timestamp.encode(toTimestamp(message.createdAt), writer.uint32(18).fork()).join();
    }
    if (message.seqNum !== 0) {
      writer.uint32(24).uint32(message.seqNum);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TraceShred {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTraceShred();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.region = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.createdAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.seqNum = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<TraceShred, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<TraceShred | TraceShred[]> | Iterable<TraceShred | TraceShred[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [TraceShred.encode(p).finish()];
        }
      } else {
        yield* [TraceShred.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, TraceShred>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<TraceShred> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [TraceShred.decode(p)];
        }
      } else {
        yield* [TraceShred.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): TraceShred {
    return {
      region: isSet(object.region) ? globalThis.String(object.region) : "",
      createdAt: isSet(object.createdAt) ? fromJsonTimestamp(object.createdAt) : undefined,
      seqNum: isSet(object.seqNum) ? globalThis.Number(object.seqNum) : 0,
    };
  },

  toJSON(message: TraceShred): unknown {
    const obj: any = {};
    if (message.region !== "") {
      obj.region = message.region;
    }
    if (message.createdAt !== undefined) {
      obj.createdAt = message.createdAt.toISOString();
    }
    if (message.seqNum !== 0) {
      obj.seqNum = Math.round(message.seqNum);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TraceShred>, I>>(base?: I): TraceShred {
    return TraceShred.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TraceShred>, I>>(object: I): TraceShred {
    const message = createBaseTraceShred();
    message.region = object.region ?? "";
    message.createdAt = object.createdAt ?? undefined;
    message.seqNum = object.seqNum ?? 0;
    return message;
  },
};

function createBaseSubscribeEntriesRequest(): SubscribeEntriesRequest {
  return {};
}

export const SubscribeEntriesRequest: MessageFns<SubscribeEntriesRequest> = {
  encode(_: SubscribeEntriesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeEntriesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeEntriesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeEntriesRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeEntriesRequest | SubscribeEntriesRequest[]>
      | Iterable<SubscribeEntriesRequest | SubscribeEntriesRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeEntriesRequest.encode(p).finish()];
        }
      } else {
        yield* [SubscribeEntriesRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeEntriesRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeEntriesRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeEntriesRequest.decode(p)];
        }
      } else {
        yield* [SubscribeEntriesRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): SubscribeEntriesRequest {
    return {};
  },

  toJSON(_: SubscribeEntriesRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeEntriesRequest>, I>>(base?: I): SubscribeEntriesRequest {
    return SubscribeEntriesRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeEntriesRequest>, I>>(_: I): SubscribeEntriesRequest {
    const message = createBaseSubscribeEntriesRequest();
    return message;
  },
};

function createBaseEntry(): Entry {
  return { slot: 0n, entries: Buffer.alloc(0) };
}

export const Entry: MessageFns<Entry> = {
  encode(message: Entry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(8).uint64(message.slot);
    }
    if (message.entries.length !== 0) {
      writer.uint32(18).bytes(message.entries);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Entry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.entries = Buffer.from(reader.bytes());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Entry, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Entry | Entry[]> | Iterable<Entry | Entry[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Entry.encode(p).finish()];
        }
      } else {
        yield* [Entry.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Entry>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Entry> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Entry.decode(p)];
        }
      } else {
        yield* [Entry.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Entry {
    return {
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      entries: isSet(object.entries) ? Buffer.from(bytesFromBase64(object.entries)) : Buffer.alloc(0),
    };
  },

  toJSON(message: Entry): unknown {
    const obj: any = {};
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.entries.length !== 0) {
      obj.entries = base64FromBytes(message.entries);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Entry>, I>>(base?: I): Entry {
    return Entry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Entry>, I>>(object: I): Entry {
    const message = createBaseEntry();
    message.slot = object.slot ?? 0n;
    message.entries = object.entries ?? Buffer.alloc(0);
    return message;
  },
};

export type ShredstreamService = typeof ShredstreamService;
export const ShredstreamService = {
  /** RPC endpoint to send heartbeats to keep shreds flowing */
  sendHeartbeat: {
    path: "/shredstream.Shredstream/SendHeartbeat",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: Heartbeat) => Buffer.from(Heartbeat.encode(value).finish()),
    requestDeserialize: (value: Buffer) => Heartbeat.decode(value),
    responseSerialize: (value: HeartbeatResponse) => Buffer.from(HeartbeatResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => HeartbeatResponse.decode(value),
  },
} as const;

export interface ShredstreamServer extends UntypedServiceImplementation {
  /** RPC endpoint to send heartbeats to keep shreds flowing */
  sendHeartbeat: handleUnaryCall<Heartbeat, HeartbeatResponse>;
}

export interface ShredstreamClient extends Client {
  /** RPC endpoint to send heartbeats to keep shreds flowing */
  sendHeartbeat(
    request: Heartbeat,
    callback: (error: ServiceError | null, response: HeartbeatResponse) => void,
  ): ClientUnaryCall;
  sendHeartbeat(
    request: Heartbeat,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: HeartbeatResponse) => void,
  ): ClientUnaryCall;
  sendHeartbeat(
    request: Heartbeat,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: HeartbeatResponse) => void,
  ): ClientUnaryCall;
}

export const ShredstreamClient = makeGenericClientConstructor(
  ShredstreamService,
  "shredstream.Shredstream",
) as unknown as {
  new (address: string, credentials: ChannelCredentials, options?: Partial<ClientOptions>): ShredstreamClient;
  service: typeof ShredstreamService;
  serviceName: string;
};

export type ShredstreamProxyService = typeof ShredstreamProxyService;
export const ShredstreamProxyService = {
  subscribeEntries: {
    path: "/shredstream.ShredstreamProxy/SubscribeEntries",
    requestStream: false,
    responseStream: true,
    requestSerialize: (value: SubscribeEntriesRequest) => Buffer.from(SubscribeEntriesRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => SubscribeEntriesRequest.decode(value),
    responseSerialize: (value: Entry) => Buffer.from(Entry.encode(value).finish()),
    responseDeserialize: (value: Buffer) => Entry.decode(value),
  },
} as const;

export interface ShredstreamProxyServer extends UntypedServiceImplementation {
  subscribeEntries: handleServerStreamingCall<SubscribeEntriesRequest, Entry>;
}

export interface ShredstreamProxyClient extends Client {
  subscribeEntries(request: SubscribeEntriesRequest, options?: Partial<CallOptions>): ClientReadableStream<Entry>;
  subscribeEntries(
    request: SubscribeEntriesRequest,
    metadata?: Metadata,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<Entry>;
}

export const ShredstreamProxyClient = makeGenericClientConstructor(
  ShredstreamProxyService,
  "shredstream.ShredstreamProxy",
) as unknown as {
  new (address: string, credentials: ChannelCredentials, options?: Partial<ClientOptions>): ShredstreamProxyClient;
  service: typeof ShredstreamProxyService;
  serviceName: string;
};

function bytesFromBase64(b64: string): Uint8Array {
  return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
}

function base64FromBytes(arr: Uint8Array): string {
  return globalThis.Buffer.from(arr).toString("base64");
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | bigint | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function toTimestamp(date: Date): Timestamp {
  const seconds = BigInt(Math.trunc(date.getTime() / 1_000));
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (globalThis.Number(t.seconds.toString()) || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

function fromJsonTimestamp(o: any): Date {
  if (o instanceof globalThis.Date) {
    return o;
  } else if (typeof o === "string") {
    return new globalThis.Date(o);
  } else {
    return fromTimestamp(Timestamp.fromJSON(o));
  }
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  encodeTransform(source: AsyncIterable<T | T[]> | Iterable<T | T[]>): AsyncIterable<Uint8Array>;
  decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<T>;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
