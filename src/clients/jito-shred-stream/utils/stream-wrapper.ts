import type { CallOptions, ClientReadableStream } from '@grpc/grpc-js'
import { StreamWrapper, type StreamWrapperOptions } from '../../../streams/stream-wrapper'
import type { Entry, ShredstreamProxyClient } from '../generated/shredstream'

export type JitoShredStreamSubscribeStream = ClientReadableStream<Entry>

export interface JitoShredStreamStreamWrapperOptions extends StreamWrapperOptions {
    callOptions?: CallOptions
}

export class JitoShredStreamStreamWrapper extends StreamWrapper<Entry> {
    public constructor(client: ShredstreamProxyClient, { callOptions, ...options }: JitoShredStreamStreamWrapperOptions = {}) {
        super(() => client.subscribeEntries({}, callOptions), options)
    }
}
