import type { CallOptions, ClientReadableStream } from '@grpc/grpc-js'
import { StreamWrapper, type StreamWrapperOptions } from '../../../streams/stream-wrapper'
import type { ShredForwarderServiceClient, SubscribeResponse } from '../generated/shred_forwarder'

export type ShredForwarderSubscribeStream = ClientReadableStream<SubscribeResponse>

export interface ShredForwarderStreamWrapperOptions extends StreamWrapperOptions {
    callOptions?: CallOptions
}

export class ShredForwarderStreamWrapper extends StreamWrapper<SubscribeResponse> {
    public constructor(client: ShredForwarderServiceClient, { callOptions, ...options }: ShredForwarderStreamWrapperOptions = {}) {
        super(() => client.subscribe({}, callOptions), options)
    }
}
