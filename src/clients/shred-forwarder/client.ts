import type { Metadata } from '@grpc/grpc-js'
import { type CreateGrpcClientOptions, call, createGrpcClient } from '../../utils'
import { type PingRequest, type PongResponse, ShredForwarderServiceClient } from './generated/shred_forwarder'
import { ShredForwarderStreamWrapper, type ShredForwarderStreamWrapperOptions } from './utils'

export type ShredForwarderClientOptions = CreateGrpcClientOptions

export class ShredForwarderClient {
    public readonly grpc: ShredForwarderServiceClient

    public constructor(url: string, protected readonly options: ShredForwarderClientOptions = {}) {
        this.grpc = createGrpcClient(ShredForwarderServiceClient, url, options)
    }

    public createStream(options?: ShredForwarderStreamWrapperOptions) {
        return new ShredForwarderStreamWrapper(this.grpc, options)
    }

    public async ping(request: PingRequest, metadata?: Metadata) {
        return call<PingRequest, PongResponse>(this.grpc.ping.bind(this.grpc), request, metadata)
    }
}
