// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.1
//   protoc               v5.29.3
// source: shred_forwarder.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  type CallOptions,
  ChannelCredentials,
  Client,
  type ClientOptions,
  type ClientReadableStream,
  type ClientUnaryCall,
  type handleServerStreamingCall,
  type handleUnaryCall,
  makeGenericClientConstructor,
  Metadata,
  type ServiceError,
  type UntypedServiceImplementation,
} from "@grpc/grpc-js";
import { Timestamp } from "./google/protobuf/timestamp";

export interface PingRequest {
  count: number;
}

export interface SubscribeRequest {
}

export interface PongResponse {
  count: number;
  timestamp: Date | undefined;
  version: string;
}

export interface SubscribeResponse {
  slot: bigint;
  entryIndex: number;
  transactionIndex: number;
  transaction: VersionedTransaction | undefined;
  receivedAt: Date | undefined;
  processingTimeNanos: bigint;
}

export interface VersionedTransaction {
  signatures: Buffer[];
  message: VersionedMessage | undefined;
}

export interface VersionedMessage {
  legacy?: LegacyMessage | undefined;
  v0?: V0Message | undefined;
}

export interface LegacyMessage {
  header: MessageHeader | undefined;
  accountKeys: Buffer[];
  recentBlockhash: Buffer;
  instructions: CompiledInstruction[];
}

export interface V0Message {
  header: MessageHeader | undefined;
  accountKeys: Buffer[];
  recentBlockhash: Buffer;
  instructions: CompiledInstruction[];
  addressTableLookups: MessageAddressTableLookup[];
}

export interface MessageHeader {
  numRequiredSignatures: number;
  numReadonlySignedAccounts: number;
  numReadonlyUnsignedAccounts: number;
}

export interface CompiledInstruction {
  programIdIndex: number;
  accounts: number[];
  data: Buffer;
}

export interface MessageAddressTableLookup {
  accountKey: Buffer;
  writableIndexes: number[];
  readonlyIndexes: number[];
}

function createBasePingRequest(): PingRequest {
  return { count: 0 };
}

export const PingRequest: MessageFns<PingRequest> = {
  encode(message: PingRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.count !== 0) {
      writer.uint32(8).int32(message.count);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PingRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePingRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<PingRequest, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<PingRequest | PingRequest[]> | Iterable<PingRequest | PingRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [PingRequest.encode(p).finish()];
        }
      } else {
        yield* [PingRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, PingRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<PingRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [PingRequest.decode(p)];
        }
      } else {
        yield* [PingRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): PingRequest {
    return { count: isSet(object.count) ? globalThis.Number(object.count) : 0 };
  },

  toJSON(message: PingRequest): unknown {
    const obj: any = {};
    if (message.count !== 0) {
      obj.count = Math.round(message.count);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PingRequest>, I>>(base?: I): PingRequest {
    return PingRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PingRequest>, I>>(object: I): PingRequest {
    const message = createBasePingRequest();
    message.count = object.count ?? 0;
    return message;
  },
};

function createBaseSubscribeRequest(): SubscribeRequest {
  return {};
}

export const SubscribeRequest: MessageFns<SubscribeRequest> = {
  encode(_: SubscribeRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeRequest, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<SubscribeRequest | SubscribeRequest[]> | Iterable<SubscribeRequest | SubscribeRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest.encode(p).finish()];
        }
      } else {
        yield* [SubscribeRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeRequest.decode(p)];
        }
      } else {
        yield* [SubscribeRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): SubscribeRequest {
    return {};
  },

  toJSON(_: SubscribeRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeRequest>, I>>(base?: I): SubscribeRequest {
    return SubscribeRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRequest>, I>>(_: I): SubscribeRequest {
    const message = createBaseSubscribeRequest();
    return message;
  },
};

function createBasePongResponse(): PongResponse {
  return { count: 0, timestamp: undefined, version: "" };
}

export const PongResponse: MessageFns<PongResponse> = {
  encode(message: PongResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.count !== 0) {
      writer.uint32(8).int32(message.count);
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(18).fork()).join();
    }
    if (message.version !== "") {
      writer.uint32(26).string(message.version);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PongResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePongResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.count = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.version = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<PongResponse, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<PongResponse | PongResponse[]> | Iterable<PongResponse | PongResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [PongResponse.encode(p).finish()];
        }
      } else {
        yield* [PongResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, PongResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<PongResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [PongResponse.decode(p)];
        }
      } else {
        yield* [PongResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): PongResponse {
    return {
      count: isSet(object.count) ? globalThis.Number(object.count) : 0,
      timestamp: isSet(object.timestamp) ? fromJsonTimestamp(object.timestamp) : undefined,
      version: isSet(object.version) ? globalThis.String(object.version) : "",
    };
  },

  toJSON(message: PongResponse): unknown {
    const obj: any = {};
    if (message.count !== 0) {
      obj.count = Math.round(message.count);
    }
    if (message.timestamp !== undefined) {
      obj.timestamp = message.timestamp.toISOString();
    }
    if (message.version !== "") {
      obj.version = message.version;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PongResponse>, I>>(base?: I): PongResponse {
    return PongResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PongResponse>, I>>(object: I): PongResponse {
    const message = createBasePongResponse();
    message.count = object.count ?? 0;
    message.timestamp = object.timestamp ?? undefined;
    message.version = object.version ?? "";
    return message;
  },
};

function createBaseSubscribeResponse(): SubscribeResponse {
  return {
    slot: 0n,
    entryIndex: 0,
    transactionIndex: 0,
    transaction: undefined,
    receivedAt: undefined,
    processingTimeNanos: 0n,
  };
}

export const SubscribeResponse: MessageFns<SubscribeResponse> = {
  encode(message: SubscribeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(8).uint64(message.slot);
    }
    if (message.entryIndex !== 0) {
      writer.uint32(16).uint32(message.entryIndex);
    }
    if (message.transactionIndex !== 0) {
      writer.uint32(24).uint32(message.transactionIndex);
    }
    if (message.transaction !== undefined) {
      VersionedTransaction.encode(message.transaction, writer.uint32(34).fork()).join();
    }
    if (message.receivedAt !== undefined) {
      Timestamp.encode(toTimestamp(message.receivedAt), writer.uint32(42).fork()).join();
    }
    if (message.processingTimeNanos !== 0n) {
      if (BigInt.asUintN(64, message.processingTimeNanos) !== message.processingTimeNanos) {
        throw new globalThis.Error("value provided for field message.processingTimeNanos of type uint64 too large");
      }
      writer.uint32(48).uint64(message.processingTimeNanos);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.entryIndex = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.transactionIndex = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.transaction = VersionedTransaction.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.receivedAt = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.processingTimeNanos = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeResponse, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<SubscribeResponse | SubscribeResponse[]> | Iterable<SubscribeResponse | SubscribeResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeResponse.encode(p).finish()];
        }
      } else {
        yield* [SubscribeResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeResponse.decode(p)];
        }
      } else {
        yield* [SubscribeResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeResponse {
    return {
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      entryIndex: isSet(object.entryIndex) ? globalThis.Number(object.entryIndex) : 0,
      transactionIndex: isSet(object.transactionIndex) ? globalThis.Number(object.transactionIndex) : 0,
      transaction: isSet(object.transaction) ? VersionedTransaction.fromJSON(object.transaction) : undefined,
      receivedAt: isSet(object.receivedAt) ? fromJsonTimestamp(object.receivedAt) : undefined,
      processingTimeNanos: isSet(object.processingTimeNanos) ? BigInt(object.processingTimeNanos) : 0n,
    };
  },

  toJSON(message: SubscribeResponse): unknown {
    const obj: any = {};
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.entryIndex !== 0) {
      obj.entryIndex = Math.round(message.entryIndex);
    }
    if (message.transactionIndex !== 0) {
      obj.transactionIndex = Math.round(message.transactionIndex);
    }
    if (message.transaction !== undefined) {
      obj.transaction = VersionedTransaction.toJSON(message.transaction);
    }
    if (message.receivedAt !== undefined) {
      obj.receivedAt = message.receivedAt.toISOString();
    }
    if (message.processingTimeNanos !== 0n) {
      obj.processingTimeNanos = message.processingTimeNanos.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeResponse>, I>>(base?: I): SubscribeResponse {
    return SubscribeResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeResponse>, I>>(object: I): SubscribeResponse {
    const message = createBaseSubscribeResponse();
    message.slot = object.slot ?? 0n;
    message.entryIndex = object.entryIndex ?? 0;
    message.transactionIndex = object.transactionIndex ?? 0;
    message.transaction = (object.transaction !== undefined && object.transaction !== null)
      ? VersionedTransaction.fromPartial(object.transaction)
      : undefined;
    message.receivedAt = object.receivedAt ?? undefined;
    message.processingTimeNanos = object.processingTimeNanos ?? 0n;
    return message;
  },
};

function createBaseVersionedTransaction(): VersionedTransaction {
  return { signatures: [], message: undefined };
}

export const VersionedTransaction: MessageFns<VersionedTransaction> = {
  encode(message: VersionedTransaction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.signatures) {
      writer.uint32(10).bytes(v!);
    }
    if (message.message !== undefined) {
      VersionedMessage.encode(message.message, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VersionedTransaction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVersionedTransaction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.signatures.push(Buffer.from(reader.bytes()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.message = VersionedMessage.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<VersionedTransaction, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<VersionedTransaction | VersionedTransaction[]>
      | Iterable<VersionedTransaction | VersionedTransaction[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [VersionedTransaction.encode(p).finish()];
        }
      } else {
        yield* [VersionedTransaction.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, VersionedTransaction>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<VersionedTransaction> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [VersionedTransaction.decode(p)];
        }
      } else {
        yield* [VersionedTransaction.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): VersionedTransaction {
    return {
      signatures: globalThis.Array.isArray(object?.signatures)
        ? object.signatures.map((e: any) => Buffer.from(bytesFromBase64(e)))
        : [],
      message: isSet(object.message) ? VersionedMessage.fromJSON(object.message) : undefined,
    };
  },

  toJSON(message: VersionedTransaction): unknown {
    const obj: any = {};
    if (message.signatures?.length) {
      obj.signatures = message.signatures.map((e) => base64FromBytes(e));
    }
    if (message.message !== undefined) {
      obj.message = VersionedMessage.toJSON(message.message);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VersionedTransaction>, I>>(base?: I): VersionedTransaction {
    return VersionedTransaction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VersionedTransaction>, I>>(object: I): VersionedTransaction {
    const message = createBaseVersionedTransaction();
    message.signatures = object.signatures?.map((e) => e) || [];
    message.message = (object.message !== undefined && object.message !== null)
      ? VersionedMessage.fromPartial(object.message)
      : undefined;
    return message;
  },
};

function createBaseVersionedMessage(): VersionedMessage {
  return { legacy: undefined, v0: undefined };
}

export const VersionedMessage: MessageFns<VersionedMessage> = {
  encode(message: VersionedMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.legacy !== undefined) {
      LegacyMessage.encode(message.legacy, writer.uint32(10).fork()).join();
    }
    if (message.v0 !== undefined) {
      V0Message.encode(message.v0, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VersionedMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVersionedMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.legacy = LegacyMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.v0 = V0Message.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<VersionedMessage, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<VersionedMessage | VersionedMessage[]> | Iterable<VersionedMessage | VersionedMessage[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [VersionedMessage.encode(p).finish()];
        }
      } else {
        yield* [VersionedMessage.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, VersionedMessage>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<VersionedMessage> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [VersionedMessage.decode(p)];
        }
      } else {
        yield* [VersionedMessage.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): VersionedMessage {
    return {
      legacy: isSet(object.legacy) ? LegacyMessage.fromJSON(object.legacy) : undefined,
      v0: isSet(object.v0) ? V0Message.fromJSON(object.v0) : undefined,
    };
  },

  toJSON(message: VersionedMessage): unknown {
    const obj: any = {};
    if (message.legacy !== undefined) {
      obj.legacy = LegacyMessage.toJSON(message.legacy);
    }
    if (message.v0 !== undefined) {
      obj.v0 = V0Message.toJSON(message.v0);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VersionedMessage>, I>>(base?: I): VersionedMessage {
    return VersionedMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VersionedMessage>, I>>(object: I): VersionedMessage {
    const message = createBaseVersionedMessage();
    message.legacy = (object.legacy !== undefined && object.legacy !== null)
      ? LegacyMessage.fromPartial(object.legacy)
      : undefined;
    message.v0 = (object.v0 !== undefined && object.v0 !== null) ? V0Message.fromPartial(object.v0) : undefined;
    return message;
  },
};

function createBaseLegacyMessage(): LegacyMessage {
  return { header: undefined, accountKeys: [], recentBlockhash: Buffer.alloc(0), instructions: [] };
}

export const LegacyMessage: MessageFns<LegacyMessage> = {
  encode(message: LegacyMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.header !== undefined) {
      MessageHeader.encode(message.header, writer.uint32(10).fork()).join();
    }
    for (const v of message.accountKeys) {
      writer.uint32(18).bytes(v!);
    }
    if (message.recentBlockhash.length !== 0) {
      writer.uint32(26).bytes(message.recentBlockhash);
    }
    for (const v of message.instructions) {
      CompiledInstruction.encode(v!, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LegacyMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLegacyMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.header = MessageHeader.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accountKeys.push(Buffer.from(reader.bytes()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.recentBlockhash = Buffer.from(reader.bytes());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.instructions.push(CompiledInstruction.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<LegacyMessage, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<LegacyMessage | LegacyMessage[]> | Iterable<LegacyMessage | LegacyMessage[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [LegacyMessage.encode(p).finish()];
        }
      } else {
        yield* [LegacyMessage.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, LegacyMessage>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<LegacyMessage> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [LegacyMessage.decode(p)];
        }
      } else {
        yield* [LegacyMessage.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): LegacyMessage {
    return {
      header: isSet(object.header) ? MessageHeader.fromJSON(object.header) : undefined,
      accountKeys: globalThis.Array.isArray(object?.accountKeys)
        ? object.accountKeys.map((e: any) => Buffer.from(bytesFromBase64(e)))
        : [],
      recentBlockhash: isSet(object.recentBlockhash)
        ? Buffer.from(bytesFromBase64(object.recentBlockhash))
        : Buffer.alloc(0),
      instructions: globalThis.Array.isArray(object?.instructions)
        ? object.instructions.map((e: any) => CompiledInstruction.fromJSON(e))
        : [],
    };
  },

  toJSON(message: LegacyMessage): unknown {
    const obj: any = {};
    if (message.header !== undefined) {
      obj.header = MessageHeader.toJSON(message.header);
    }
    if (message.accountKeys?.length) {
      obj.accountKeys = message.accountKeys.map((e) => base64FromBytes(e));
    }
    if (message.recentBlockhash.length !== 0) {
      obj.recentBlockhash = base64FromBytes(message.recentBlockhash);
    }
    if (message.instructions?.length) {
      obj.instructions = message.instructions.map((e) => CompiledInstruction.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LegacyMessage>, I>>(base?: I): LegacyMessage {
    return LegacyMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LegacyMessage>, I>>(object: I): LegacyMessage {
    const message = createBaseLegacyMessage();
    message.header = (object.header !== undefined && object.header !== null)
      ? MessageHeader.fromPartial(object.header)
      : undefined;
    message.accountKeys = object.accountKeys?.map((e) => e) || [];
    message.recentBlockhash = object.recentBlockhash ?? Buffer.alloc(0);
    message.instructions = object.instructions?.map((e) => CompiledInstruction.fromPartial(e)) || [];
    return message;
  },
};

function createBaseV0Message(): V0Message {
  return {
    header: undefined,
    accountKeys: [],
    recentBlockhash: Buffer.alloc(0),
    instructions: [],
    addressTableLookups: [],
  };
}

export const V0Message: MessageFns<V0Message> = {
  encode(message: V0Message, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.header !== undefined) {
      MessageHeader.encode(message.header, writer.uint32(10).fork()).join();
    }
    for (const v of message.accountKeys) {
      writer.uint32(18).bytes(v!);
    }
    if (message.recentBlockhash.length !== 0) {
      writer.uint32(26).bytes(message.recentBlockhash);
    }
    for (const v of message.instructions) {
      CompiledInstruction.encode(v!, writer.uint32(34).fork()).join();
    }
    for (const v of message.addressTableLookups) {
      MessageAddressTableLookup.encode(v!, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): V0Message {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseV0Message();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.header = MessageHeader.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accountKeys.push(Buffer.from(reader.bytes()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.recentBlockhash = Buffer.from(reader.bytes());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.instructions.push(CompiledInstruction.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.addressTableLookups.push(MessageAddressTableLookup.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<V0Message, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<V0Message | V0Message[]> | Iterable<V0Message | V0Message[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [V0Message.encode(p).finish()];
        }
      } else {
        yield* [V0Message.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, V0Message>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<V0Message> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [V0Message.decode(p)];
        }
      } else {
        yield* [V0Message.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): V0Message {
    return {
      header: isSet(object.header) ? MessageHeader.fromJSON(object.header) : undefined,
      accountKeys: globalThis.Array.isArray(object?.accountKeys)
        ? object.accountKeys.map((e: any) => Buffer.from(bytesFromBase64(e)))
        : [],
      recentBlockhash: isSet(object.recentBlockhash)
        ? Buffer.from(bytesFromBase64(object.recentBlockhash))
        : Buffer.alloc(0),
      instructions: globalThis.Array.isArray(object?.instructions)
        ? object.instructions.map((e: any) => CompiledInstruction.fromJSON(e))
        : [],
      addressTableLookups: globalThis.Array.isArray(object?.addressTableLookups)
        ? object.addressTableLookups.map((e: any) => MessageAddressTableLookup.fromJSON(e))
        : [],
    };
  },

  toJSON(message: V0Message): unknown {
    const obj: any = {};
    if (message.header !== undefined) {
      obj.header = MessageHeader.toJSON(message.header);
    }
    if (message.accountKeys?.length) {
      obj.accountKeys = message.accountKeys.map((e) => base64FromBytes(e));
    }
    if (message.recentBlockhash.length !== 0) {
      obj.recentBlockhash = base64FromBytes(message.recentBlockhash);
    }
    if (message.instructions?.length) {
      obj.instructions = message.instructions.map((e) => CompiledInstruction.toJSON(e));
    }
    if (message.addressTableLookups?.length) {
      obj.addressTableLookups = message.addressTableLookups.map((e) => MessageAddressTableLookup.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<V0Message>, I>>(base?: I): V0Message {
    return V0Message.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<V0Message>, I>>(object: I): V0Message {
    const message = createBaseV0Message();
    message.header = (object.header !== undefined && object.header !== null)
      ? MessageHeader.fromPartial(object.header)
      : undefined;
    message.accountKeys = object.accountKeys?.map((e) => e) || [];
    message.recentBlockhash = object.recentBlockhash ?? Buffer.alloc(0);
    message.instructions = object.instructions?.map((e) => CompiledInstruction.fromPartial(e)) || [];
    message.addressTableLookups = object.addressTableLookups?.map((e) => MessageAddressTableLookup.fromPartial(e)) ||
      [];
    return message;
  },
};

function createBaseMessageHeader(): MessageHeader {
  return { numRequiredSignatures: 0, numReadonlySignedAccounts: 0, numReadonlyUnsignedAccounts: 0 };
}

export const MessageHeader: MessageFns<MessageHeader> = {
  encode(message: MessageHeader, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.numRequiredSignatures !== 0) {
      writer.uint32(8).uint32(message.numRequiredSignatures);
    }
    if (message.numReadonlySignedAccounts !== 0) {
      writer.uint32(16).uint32(message.numReadonlySignedAccounts);
    }
    if (message.numReadonlyUnsignedAccounts !== 0) {
      writer.uint32(24).uint32(message.numReadonlyUnsignedAccounts);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MessageHeader {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMessageHeader();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.numRequiredSignatures = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.numReadonlySignedAccounts = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.numReadonlyUnsignedAccounts = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<MessageHeader, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<MessageHeader | MessageHeader[]> | Iterable<MessageHeader | MessageHeader[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageHeader.encode(p).finish()];
        }
      } else {
        yield* [MessageHeader.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, MessageHeader>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<MessageHeader> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageHeader.decode(p)];
        }
      } else {
        yield* [MessageHeader.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): MessageHeader {
    return {
      numRequiredSignatures: isSet(object.numRequiredSignatures) ? globalThis.Number(object.numRequiredSignatures) : 0,
      numReadonlySignedAccounts: isSet(object.numReadonlySignedAccounts)
        ? globalThis.Number(object.numReadonlySignedAccounts)
        : 0,
      numReadonlyUnsignedAccounts: isSet(object.numReadonlyUnsignedAccounts)
        ? globalThis.Number(object.numReadonlyUnsignedAccounts)
        : 0,
    };
  },

  toJSON(message: MessageHeader): unknown {
    const obj: any = {};
    if (message.numRequiredSignatures !== 0) {
      obj.numRequiredSignatures = Math.round(message.numRequiredSignatures);
    }
    if (message.numReadonlySignedAccounts !== 0) {
      obj.numReadonlySignedAccounts = Math.round(message.numReadonlySignedAccounts);
    }
    if (message.numReadonlyUnsignedAccounts !== 0) {
      obj.numReadonlyUnsignedAccounts = Math.round(message.numReadonlyUnsignedAccounts);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MessageHeader>, I>>(base?: I): MessageHeader {
    return MessageHeader.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MessageHeader>, I>>(object: I): MessageHeader {
    const message = createBaseMessageHeader();
    message.numRequiredSignatures = object.numRequiredSignatures ?? 0;
    message.numReadonlySignedAccounts = object.numReadonlySignedAccounts ?? 0;
    message.numReadonlyUnsignedAccounts = object.numReadonlyUnsignedAccounts ?? 0;
    return message;
  },
};

function createBaseCompiledInstruction(): CompiledInstruction {
  return { programIdIndex: 0, accounts: [], data: Buffer.alloc(0) };
}

export const CompiledInstruction: MessageFns<CompiledInstruction> = {
  encode(message: CompiledInstruction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.programIdIndex !== 0) {
      writer.uint32(8).uint32(message.programIdIndex);
    }
    writer.uint32(18).fork();
    for (const v of message.accounts) {
      writer.uint32(v);
    }
    writer.join();
    if (message.data.length !== 0) {
      writer.uint32(26).bytes(message.data);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompiledInstruction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompiledInstruction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.programIdIndex = reader.uint32();
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.accounts.push(reader.uint32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.accounts.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.data = Buffer.from(reader.bytes());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<CompiledInstruction, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<CompiledInstruction | CompiledInstruction[]>
      | Iterable<CompiledInstruction | CompiledInstruction[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [CompiledInstruction.encode(p).finish()];
        }
      } else {
        yield* [CompiledInstruction.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, CompiledInstruction>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<CompiledInstruction> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [CompiledInstruction.decode(p)];
        }
      } else {
        yield* [CompiledInstruction.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): CompiledInstruction {
    return {
      programIdIndex: isSet(object.programIdIndex) ? globalThis.Number(object.programIdIndex) : 0,
      accounts: globalThis.Array.isArray(object?.accounts) ? object.accounts.map((e: any) => globalThis.Number(e)) : [],
      data: isSet(object.data) ? Buffer.from(bytesFromBase64(object.data)) : Buffer.alloc(0),
    };
  },

  toJSON(message: CompiledInstruction): unknown {
    const obj: any = {};
    if (message.programIdIndex !== 0) {
      obj.programIdIndex = Math.round(message.programIdIndex);
    }
    if (message.accounts?.length) {
      obj.accounts = message.accounts.map((e) => Math.round(e));
    }
    if (message.data.length !== 0) {
      obj.data = base64FromBytes(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CompiledInstruction>, I>>(base?: I): CompiledInstruction {
    return CompiledInstruction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompiledInstruction>, I>>(object: I): CompiledInstruction {
    const message = createBaseCompiledInstruction();
    message.programIdIndex = object.programIdIndex ?? 0;
    message.accounts = object.accounts?.map((e) => e) || [];
    message.data = object.data ?? Buffer.alloc(0);
    return message;
  },
};

function createBaseMessageAddressTableLookup(): MessageAddressTableLookup {
  return { accountKey: Buffer.alloc(0), writableIndexes: [], readonlyIndexes: [] };
}

export const MessageAddressTableLookup: MessageFns<MessageAddressTableLookup> = {
  encode(message: MessageAddressTableLookup, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.accountKey.length !== 0) {
      writer.uint32(10).bytes(message.accountKey);
    }
    writer.uint32(18).fork();
    for (const v of message.writableIndexes) {
      writer.uint32(v);
    }
    writer.join();
    writer.uint32(26).fork();
    for (const v of message.readonlyIndexes) {
      writer.uint32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MessageAddressTableLookup {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMessageAddressTableLookup();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accountKey = Buffer.from(reader.bytes());
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.writableIndexes.push(reader.uint32());

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.writableIndexes.push(reader.uint32());
            }

            continue;
          }

          break;
        }
        case 3: {
          if (tag === 24) {
            message.readonlyIndexes.push(reader.uint32());

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.readonlyIndexes.push(reader.uint32());
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<MessageAddressTableLookup, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<MessageAddressTableLookup | MessageAddressTableLookup[]>
      | Iterable<MessageAddressTableLookup | MessageAddressTableLookup[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageAddressTableLookup.encode(p).finish()];
        }
      } else {
        yield* [MessageAddressTableLookup.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, MessageAddressTableLookup>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<MessageAddressTableLookup> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [MessageAddressTableLookup.decode(p)];
        }
      } else {
        yield* [MessageAddressTableLookup.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): MessageAddressTableLookup {
    return {
      accountKey: isSet(object.accountKey) ? Buffer.from(bytesFromBase64(object.accountKey)) : Buffer.alloc(0),
      writableIndexes: globalThis.Array.isArray(object?.writableIndexes)
        ? object.writableIndexes.map((e: any) => globalThis.Number(e))
        : [],
      readonlyIndexes: globalThis.Array.isArray(object?.readonlyIndexes)
        ? object.readonlyIndexes.map((e: any) => globalThis.Number(e))
        : [],
    };
  },

  toJSON(message: MessageAddressTableLookup): unknown {
    const obj: any = {};
    if (message.accountKey.length !== 0) {
      obj.accountKey = base64FromBytes(message.accountKey);
    }
    if (message.writableIndexes?.length) {
      obj.writableIndexes = message.writableIndexes.map((e) => Math.round(e));
    }
    if (message.readonlyIndexes?.length) {
      obj.readonlyIndexes = message.readonlyIndexes.map((e) => Math.round(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MessageAddressTableLookup>, I>>(base?: I): MessageAddressTableLookup {
    return MessageAddressTableLookup.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MessageAddressTableLookup>, I>>(object: I): MessageAddressTableLookup {
    const message = createBaseMessageAddressTableLookup();
    message.accountKey = object.accountKey ?? Buffer.alloc(0);
    message.writableIndexes = object.writableIndexes?.map((e) => e) || [];
    message.readonlyIndexes = object.readonlyIndexes?.map((e) => e) || [];
    return message;
  },
};

export type ShredForwarderServiceService = typeof ShredForwarderServiceService;
export const ShredForwarderServiceService = {
  ping: {
    path: "/shred_forwarder.ShredForwarderService/Ping",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: PingRequest) => Buffer.from(PingRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => PingRequest.decode(value),
    responseSerialize: (value: PongResponse) => Buffer.from(PongResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => PongResponse.decode(value),
  },
  subscribe: {
    path: "/shred_forwarder.ShredForwarderService/Subscribe",
    requestStream: false,
    responseStream: true,
    requestSerialize: (value: SubscribeRequest) => Buffer.from(SubscribeRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => SubscribeRequest.decode(value),
    responseSerialize: (value: SubscribeResponse) => Buffer.from(SubscribeResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => SubscribeResponse.decode(value),
  },
} as const;

export interface ShredForwarderServiceServer extends UntypedServiceImplementation {
  ping: handleUnaryCall<PingRequest, PongResponse>;
  subscribe: handleServerStreamingCall<SubscribeRequest, SubscribeResponse>;
}

export interface ShredForwarderServiceClient extends Client {
  ping(request: PingRequest, callback: (error: ServiceError | null, response: PongResponse) => void): ClientUnaryCall;
  ping(
    request: PingRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: PongResponse) => void,
  ): ClientUnaryCall;
  ping(
    request: PingRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: PongResponse) => void,
  ): ClientUnaryCall;
  subscribe(request: SubscribeRequest, options?: Partial<CallOptions>): ClientReadableStream<SubscribeResponse>;
  subscribe(
    request: SubscribeRequest,
    metadata?: Metadata,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<SubscribeResponse>;
}

export const ShredForwarderServiceClient = makeGenericClientConstructor(
  ShredForwarderServiceService,
  "shred_forwarder.ShredForwarderService",
) as unknown as {
  new (address: string, credentials: ChannelCredentials, options?: Partial<ClientOptions>): ShredForwarderServiceClient;
  service: typeof ShredForwarderServiceService;
  serviceName: string;
};

function bytesFromBase64(b64: string): Uint8Array {
  return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
}

function base64FromBytes(arr: Uint8Array): string {
  return globalThis.Buffer.from(arr).toString("base64");
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | bigint | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function toTimestamp(date: Date): Timestamp {
  const seconds = BigInt(Math.trunc(date.getTime() / 1_000));
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (globalThis.Number(t.seconds.toString()) || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

function fromJsonTimestamp(o: any): Date {
  if (o instanceof globalThis.Date) {
    return o;
  } else if (typeof o === "string") {
    return new globalThis.Date(o);
  } else {
    return fromTimestamp(Timestamp.fromJSON(o));
  }
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  encodeTransform(source: AsyncIterable<T | T[]> | Iterable<T | T[]>): AsyncIterable<Uint8Array>;
  decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<T>;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
