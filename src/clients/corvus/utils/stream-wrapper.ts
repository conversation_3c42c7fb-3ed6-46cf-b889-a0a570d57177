import type { ClientDuplexStream } from '@grpc/grpc-js'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { YellowstoneGeyserStreamWrapper, type YellowstoneGeyserStreamWrapperOptions } from '../../yellowstone'
import type { ARPCServiceClient, SubscribeRequest, SubscribeResponse } from '../generated/arpc'
import { resolveHeartbeatOptions } from './options'

export type CorvusArpcSubscribeStream = ClientDuplexStream<SubscribeRequest, SubscribeResponse>

export type CorvusArpcStreamWrapperOptions = YellowstoneGeyserStreamWrapperOptions<SubscribeRequest>

export class CorvusArpcStreamWrapper extends YellowstoneGeyserStreamWrapper<SubscribeRequest, SubscribeResponse> {
    public constructor(client: ARPCServiceClient, { heartbeat = true, ...options }: CorvusArpcStreamWrapperOptions = {}) {
        super(client, { ...options, heartbeat: resolveHeartbeatOptions(heartbeat) })
    }

    protected override isPingRequestMessage(request: SubscribeRequest) {
        return notNullish(request.pingId)
    }

    protected override isPingResponseMessage(response: SubscribeResponse) {
        return isNullish(response.transaction) && response.filters.length === 0
    }
}
