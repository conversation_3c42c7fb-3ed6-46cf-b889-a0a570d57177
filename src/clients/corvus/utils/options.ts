import { resolveNestedOptions } from '@kdt310722/utils/object'
import type { HeartbeatManagerOptions } from '../../../streams/heartbeat-manager'
import type { SubscribeRequest } from '../generated/arpc'

export function resolveHeartbeatOptions(options: HeartbeatManagerOptions<SubscribeRequest> | boolean) {
    const resolved = resolveNestedOptions(options) || { enabled: false }

    resolved.message ??= { pingId: 1, transactions: {} }

    return resolved
}
