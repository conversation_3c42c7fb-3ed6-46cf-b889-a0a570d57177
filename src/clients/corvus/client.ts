import { type CreateGrpcClientOptions, createGrpcClient } from '../../utils'
import { ARPCServiceClient } from './generated/arpc'
import type { CorvusArpcStreamWrapperOptions } from './utils'
import { CorvusArpcStreamWrapper } from './utils/stream-wrapper'

export type CorvusArpcClientOptions = CreateGrpcClientOptions

export class CorvusArpcClient {
    public readonly grpc: ARPCServiceClient

    public constructor(url: string, protected readonly options: CorvusArpcClientOptions = {}) {
        this.grpc = createGrpcClient(ARPCServiceClient, url, options)
    }

    public createStream(options?: CorvusArpcStreamWrapperOptions) {
        return new CorvusArpcStreamWrapper(this.grpc, options)
    }
}
