# Changelog


## v0.0.2

[compare changes](https://github.com/kdt-farm/solana-grpc-client/compare/v0.0.1...v0.0.2)

### 🚀 Enhancements

- Add shred-forwarder ([eedbc70](https://github.com/kdt-farm/solana-grpc-client/commit/eedbc70))
- Add jito-shred-stream ([4f53630](https://github.com/kdt-farm/solana-grpc-client/commit/4f53630))
- Jito shredstream ([ef7f84e](https://github.com/kdt-farm/solana-grpc-client/commit/ef7f84e))

### 🩹 Fixes

- Add default type parameters to YellowstoneGeyserStreamWrapper ([1172a4e](https://github.com/kdt-farm/solana-grpc-client/commit/1172a4e))
- Add '.github' to eslint ignores ([d502ad0](https://github.com/kdt-farm/solana-grpc-client/commit/d502ad0))

### 📖 Documentation

- Update copilot instructions for new gRPC clients and guidelines ([0a86160](https://github.com/kdt-farm/solana-grpc-client/commit/0a86160))

### ❤️ Contributors

- DiepPk <<EMAIL>>

## v0.0.1


### 🏡 Chore

- Initial commit ([696b064](https://github.com/kdt-farm/solana-grpc-client/commit/696b064))

### ❤️ Contributors

- DiepPk <<EMAIL>>

